# Getting Started with C# Script Routines

Your Unity MelonLoader mod now has a powerful C# scripting system with hot reloading! Here's how to use it:

## Quick Start

1. **Start your game** with the mod loaded
2. **Press F10** to reload all scripts (or they'll auto-load)
3. **Press F9** to toggle the script management UI
4. **Edit any script** in `TestLE/Scripts/Routines/` and save - it will automatically reload!

## What's Already Set Up

✅ **Hot Reloading System** - Scripts automatically recompile when you save changes  
✅ **Integration with Main Routine** - Scripts run as part of your bot's task system  
✅ **Example Scripts** - Several working examples to learn from  
✅ **Game API Access** - Scripts can access player, enemies, items, etc.  
✅ **Coroutine Support** - Full Unity coroutine support for complex timing  

## Current Script Setup

The `SimpleTestRoutine` is currently active and will handle:
- ⚔️ **Combat** - Automatically fights nearby enemies
- 💰 **Looting** - Picks up nearby ground items  
- 🚶 **Movement** - Moves around when idle
- 🔧 **Stuck Detection** - Handles when player gets stuck

## Testing Your Scripts

1. **Load the game** and start your routine (F-key or UI button)
2. **Watch the console** for script log messages
3. **Use the GUI** - Script info appears in bottom-left corner
4. **Test controls**:
   - `T` key: Show current script status
   - `R` key: Reset script state
   - `L` key: Show nearby loot count (in loot script)
   - `C` key: Show nearby enemy count (in combat script)

## Making Changes

### Quick Edits
1. Open any `.cs` file in `TestLE/Scripts/Routines/`
2. Make your changes (add logging, change behavior, etc.)
3. **Save the file** - it will automatically reload!
4. Check the console for compilation results

### Example Quick Change
Edit `SimpleTestRoutine.cs` and change this line:
```csharp
private const float COMBAT_RANGE = 12f;
```
to:
```csharp
private const float COMBAT_RANGE = 20f; // Longer combat range
```
Save the file and the change takes effect immediately!

## Switching Between Scripts

In `TestLE/Routine/Factories/TaskFactory.cs`, you can comment/uncomment different scripts:

```csharp
// All-in-one routine (currently active)
new CSharpRoutine("SimpleTestRoutine"),

// Specialized routines (currently commented out)
// new CSharpRoutine("ExampleCombatRoutine"),
// new CSharpRoutine("ExampleLootRoutine"),  
// new CSharpRoutine("ExampleNavigationRoutine"),
```

After changing this, restart your routine for changes to take effect.

## Creating Your Own Script

1. **Copy an existing script** as a template
2. **Rename the class** and file
3. **Modify the behavior** in the `Execute()` method
4. **Add it to TaskFactory** if you want it to run automatically

### Minimal Script Template
```csharp
using System.Collections;
using TestLE.Scripting;

public class MyCustomScript : ScriptBase
{
    public override string Name => "My Custom Script";
    
    public override bool CanExecute()
    {
        return Player != null; // Run when player exists
    }
    
    public override IEnumerator Execute()
    {
        Log("Hello from my custom script!");
        yield return Wait(1f); // Wait 1 second
        Log("Script completed!");
    }
}
```

## Debugging Tips

- **Check the console** for compilation errors and script logs
- **Use `Log()`, `LogWarning()`, `LogError()`** for debugging
- **Add temporary logging** to see what your script is doing
- **Use the GUI** to display real-time information
- **Test small changes** incrementally

## Common Patterns

### State Management
```csharp
private enum MyState { Idle, Working, Done }
private MyState _currentState = MyState.Idle;
```

### Cooldowns
```csharp
private float _lastActionTime = 0f;
private const float COOLDOWN = 5f;

if (Time.time - _lastActionTime > COOLDOWN)
{
    // Do action
    _lastActionTime = Time.time;
}
```

### Finding Objects
```csharp
var nearbyEnemies = Enemies.Where(e => 
    Vector3.Distance(Player.transform.position, e.Data.transform.position) < 10f);
```

## Next Steps

1. **Try editing `SimpleTestRoutine.cs`** to change behavior
2. **Create your own script** based on the examples
3. **Experiment with different priorities** in TaskFactory
4. **Add GUI elements** to monitor your script's state
5. **Combine multiple specialized scripts** for complex behaviors

## Need Help?

- Check the example scripts for patterns
- Look at the `ScriptBase` class for available methods
- Use the console logs to debug issues
- Start simple and add complexity gradually

Happy scripting! 🚀
