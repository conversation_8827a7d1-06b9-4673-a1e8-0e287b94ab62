using System.Collections;
using System.Linq;
using TestLE.Scripting;
using TestLE.Utilities;
using UnityEngine;

/// <summary>
/// Example script routine that demonstrates looting functionality.
/// This script will automatically loot nearby items when conditions are met.
/// </summary>
public class ExampleLootRoutine : ScriptBase, IUpdatableScript
{
    public override string Name => "Example Loot Routine";
    
    private float _lastLootTime = 0f;
    private const float LOOT_COOLDOWN = 2f; // Loot every 2 seconds
    private const float LOOT_RANGE = 10f; // Loot items within 10 units
    
    public override bool CanExecute()
    {
        // Only execute if player exists and there are ground items nearby
        if (Player == null) return false;
        
        var nearbyItems = GetNearbyGroundItems();
        return nearbyItems.Any() && Time.time - _lastLootTime > LOOT_COOLDOWN;
    }
    
    public override IEnumerator Execute()
    {
        Log("Starting loot routine");
        
        var nearbyItems = GetNearbyGroundItems();
        
        foreach (var item in nearbyItems.Take(3)) // Loot up to 3 items per execution
        {
            if (item == null || item.gameObject == null) continue;
            
            Log($"Moving to loot item: {item.name}");
            
            // Move to the item
            yield return PlayerHelpers.MoveToForce(item.transform.position);
            
            // Wait a bit for movement to complete
            yield return Wait(0.5f);
            
            // Try to loot the item
            try
            {
                item.ObjectClick(Player.gameObject, true);
                Log($"Looted item: {item.name}");
                yield return Wait(0.2f); // Small delay between loots
            }
            catch (System.Exception ex)
            {
                LogError($"Failed to loot item {item.name}: {ex.Message}");
            }
        }
        
        _lastLootTime = Time.time;
        Log("Loot routine completed");
    }
    
    public void OnUpdate()
    {
        // This method is called every frame when the script is running
        // You can use this for continuous monitoring or state updates
        
        // Example: Show debug info about nearby items
        if (GetKeyDown(KeyCode.L))
        {
            var nearbyItems = GetNearbyGroundItems();
            Log($"Found {nearbyItems.Count()} nearby items");
        }
    }
    
    private System.Collections.Generic.IEnumerable<GroundItem> GetNearbyGroundItems()
    {
        if (Player == null) return System.Linq.Enumerable.Empty<GroundItem>();
        
        var playerPos = Player.transform.position;
        return GroundItems.Where(item => 
            item != null && 
            item.gameObject != null && 
            Vector3.Distance(playerPos, item.transform.position) <= LOOT_RANGE);
    }
    
    public override void OnLoad()
    {
        base.OnLoad();
        Log("Loot routine script loaded and ready!");
    }
    
    public override void OnUnload()
    {
        base.OnUnload();
        Log("Loot routine script unloaded");
    }
}
