using System.Collections;
using System.Linq;
using TestLE.Scripting;
using TestLE.Utilities;
using UnityEngine;

/// <summary>
/// Example script routine that demonstrates navigation and exploration.
/// This script will move to waypoints, interact with objects, and handle navigation tasks.
/// </summary>
public class ExampleNavigationRoutine : ScriptBase, IUpdatableScript
{
    public override string Name => "Example Navigation Routine";
    
    private Vector3[] _waypoints = new Vector3[]
    {
        new Vector3(10, 0, 10),
        new Vector3(-10, 0, 10),
        new Vector3(-10, 0, -10),
        new Vector3(10, 0, -10)
    };
    
    private int _currentWaypointIndex = 0;
    private bool _isPatrolling = false;
    private float _lastWaypointTime = 0f;
    private const float WAYPOINT_REACH_DISTANCE = 2f;
    private const float INTERACTION_RANGE = 5f;
    
    public override bool CanExecute()
    {
        if (Player == null) return false;
        
        // Execute if we're not currently at a waypoint or if it's been a while
        if (!_isPatrolling) return true;
        
        var currentPos = Player.transform.position;
        var targetWaypoint = _waypoints[_currentWaypointIndex];
        var distance = Vector3.Distance(currentPos, targetWaypoint);
        
        return distance > WAYPOINT_REACH_DISTANCE || Time.time - _lastWaypointTime > 30f;
    }
    
    public override IEnumerator Execute()
    {
        Log("Starting navigation routine");
        
        // Check for nearby interactables first
        var nearbyInteractable = GetNearestInteractable();
        if (nearbyInteractable != null)
        {
            yield return HandleInteraction(nearbyInteractable);
        }
        
        // Continue with waypoint navigation
        yield return NavigateToNextWaypoint();
        
        Log("Navigation routine completed");
    }
    
    private IEnumerator NavigateToNextWaypoint()
    {
        if (_waypoints.Length == 0)
        {
            LogWarning("No waypoints defined");
            yield break;
        }
        
        var targetWaypoint = _waypoints[_currentWaypointIndex];
        var currentPos = Player.transform.position;
        var distance = Vector3.Distance(currentPos, targetWaypoint);
        
        Log($"Navigating to waypoint {_currentWaypointIndex + 1}: {targetWaypoint}");
        
        if (distance > WAYPOINT_REACH_DISTANCE)
        {
            _isPatrolling = true;
            
            // Move to the waypoint
            yield return PlayerHelpers.MoveToForce(targetWaypoint);
            
            // Wait a moment to ensure we've reached the destination
            yield return Wait(1f);
            
            // Check if we actually reached the waypoint
            var newDistance = Vector3.Distance(Player.transform.position, targetWaypoint);
            if (newDistance <= WAYPOINT_REACH_DISTANCE)
            {
                Log($"Reached waypoint {_currentWaypointIndex + 1}");
                _currentWaypointIndex = (_currentWaypointIndex + 1) % _waypoints.Length;
                _lastWaypointTime = Time.time;
            }
            else
            {
                LogWarning($"Failed to reach waypoint {_currentWaypointIndex + 1}, distance: {newDistance}");
            }
        }
        else
        {
            Log($"Already at waypoint {_currentWaypointIndex + 1}");
            _currentWaypointIndex = (_currentWaypointIndex + 1) % _waypoints.Length;
            _lastWaypointTime = Time.time;
        }
        
        _isPatrolling = false;
    }
    
    private IEnumerator HandleInteraction(WorldObjectClickListener interactable)
    {
        if (interactable?.gameObject == null) yield break;
        
        Log($"Found interactable: {interactable.name}");
        
        var interactablePos = interactable.transform.position;
        var currentPos = Player.transform.position;
        var distance = Vector3.Distance(currentPos, interactablePos);
        
        if (distance > INTERACTION_RANGE)
        {
            Log($"Moving to interactable: {interactable.name}");
            yield return PlayerHelpers.MoveToForce(interactablePos);
            yield return Wait(0.5f);
        }
        
        try
        {
            Log($"Interacting with: {interactable.name}");
            interactable.ObjectClick(Player.gameObject, true);
            yield return Wait(1f); // Wait for interaction to complete
        }
        catch (System.Exception ex)
        {
            LogError($"Failed to interact with {interactable.name}: {ex.Message}");
        }
    }
    
    public void OnUpdate()
    {
        // Handle input for manual waypoint control
        if (GetKeyDown(KeyCode.N))
        {
            // Skip to next waypoint
            _currentWaypointIndex = (_currentWaypointIndex + 1) % _waypoints.Length;
            Log($"Manually skipped to waypoint {_currentWaypointIndex + 1}");
        }
        
        if (GetKeyDown(KeyCode.P))
        {
            // Go to previous waypoint
            _currentWaypointIndex = (_currentWaypointIndex - 1 + _waypoints.Length) % _waypoints.Length;
            Log($"Manually went back to waypoint {_currentWaypointIndex + 1}");
        }
        
        if (GetKeyDown(KeyCode.I))
        {
            // Show nearby interactables
            var interactables = GetNearbyInteractables().ToList();
            Log($"Found {interactables.Count} nearby interactables");
            foreach (var obj in interactables.Take(5))
            {
                Log($"  - {obj.name} at distance {Vector3.Distance(Player.transform.position, obj.transform.position):F1}");
            }
        }
    }
    
    private WorldObjectClickListener? GetNearestInteractable()
    {
        if (Player == null) return null;
        
        var playerPos = Player.transform.position;
        return Interactables
            .Where(obj => obj?.gameObject != null && obj.gameObject.activeInHierarchy)
            .OrderBy(obj => Vector3.Distance(playerPos, obj.transform.position))
            .FirstOrDefault(obj => Vector3.Distance(playerPos, obj.transform.position) <= INTERACTION_RANGE);
    }
    
    private System.Collections.Generic.IEnumerable<WorldObjectClickListener> GetNearbyInteractables()
    {
        if (Player == null) return System.Linq.Enumerable.Empty<WorldObjectClickListener>();
        
        var playerPos = Player.transform.position;
        return Interactables.Where(obj => 
            obj?.gameObject != null && 
            obj.gameObject.activeInHierarchy &&
            Vector3.Distance(playerPos, obj.transform.position) <= INTERACTION_RANGE * 2);
    }
    
    public override void OnLoad()
    {
        base.OnLoad();
        _currentWaypointIndex = 0;
        _isPatrolling = false;
        _lastWaypointTime = Time.time;
        Log("Navigation routine script loaded and ready!");
        Log($"Configured with {_waypoints.Length} waypoints");
    }
    
    public override void OnUnload()
    {
        base.OnUnload();
        _isPatrolling = false;
        Log("Navigation routine script unloaded");
    }
}
