# C# Script Routines

This directory contains C# script routines that can be hot-reloaded during runtime. These scripts provide a flexible way to create and modify bot behaviors without recompiling the entire mod.

## How It Works

1. **Hot Reloading**: Scripts are automatically recompiled when you save changes to the files
2. **Coroutine Support**: All scripts can use Unity coroutines for complex timing and state management
3. **Game API Access**: Scripts inherit from `ScriptBase` which provides access to game objects and utilities
4. **Multiple Interfaces**: Scripts can implement `IUpdatableScript` for frame updates and `IGuiScript` for UI rendering

## Key Features

- **Automatic Compilation**: Uses Roslyn compiler to compile scripts at runtime
- **File Watching**: Automatically detects file changes and reloads scripts
- **Error Handling**: Compilation errors are logged without crashing the mod
- **Integration**: Scripts work seamlessly with the existing routine system

## Controls

- **F10**: Reload all scripts manually
- **F9**: Toggle script UI visibility

## Example Scripts

### ExampleLootRoutine.cs
Demonstrates automatic looting functionality:
- Finds nearby ground items
- Moves to items and loots them
- Implements cooldown and range checking
- Press `L` to see nearby items count

### ExampleCombatRoutine.cs
Shows combat behavior implementation:
- Engages nearby enemies
- Handles fleeing when health is low
- Uses health potions automatically
- Displays combat info GUI
- Press `C` to see nearby enemies count

### ExampleNavigationRoutine.cs
Illustrates waypoint navigation:
- Patrols between predefined waypoints
- Interacts with nearby objects
- Handles movement and pathfinding
- Press `N` for next waypoint, `P` for previous
- Press `I` to see nearby interactables

## Creating Your Own Scripts

1. Create a new `.cs` file in this directory
2. Inherit from `ScriptBase`
3. Implement the required methods:
   - `CanExecute()`: Return true when the script should run
   - `Execute()`: Main coroutine logic
4. Optionally implement `IUpdatableScript` for frame updates
5. Optionally implement `IGuiScript` for custom UI

### Basic Template

```csharp
using System.Collections;
using TestLE.Scripting;
using UnityEngine;

public class MyCustomRoutine : ScriptBase
{
    public override string Name => "My Custom Routine";
    
    public override bool CanExecute()
    {
        // Return true when this script should execute
        return Player != null;
    }
    
    public override IEnumerator Execute()
    {
        Log("Starting my custom routine");
        
        // Your coroutine logic here
        yield return Wait(1f);
        
        Log("Custom routine completed");
    }
}
```

## Available APIs

Scripts have access to:
- `Player`: Current player object
- `Enemies`: List of current enemies
- `GroundItems`: List of items on the ground
- `Interactables`: List of interactable objects
- `CurrentScene`: Current scene name
- `Log()`, `LogWarning()`, `LogError()`: Logging methods
- `Wait()`, `WaitFrame()`: Coroutine utilities
- `GetKey()`, `GetKeyDown()`, `GetKeyUp()`: Input handling
- `GetNearestEnemy()`: Find closest enemy

## Integration with Main Routine

To use scripts in your main routine system:

1. Add a `CSharpRoutine` task to your routine:
```csharp
var scriptTask = new CSharpRoutine("ExampleLootRoutine");
```

2. The script will be automatically loaded and executed when conditions are met

## Tips

- Use descriptive names for your scripts
- Implement proper error handling in your scripts
- Use the logging methods for debugging
- Test scripts incrementally - start simple and add complexity
- Remember that scripts are reloaded on file save, so state is reset
- Use the `OnLoad()` and `OnUnload()` methods for initialization and cleanup
