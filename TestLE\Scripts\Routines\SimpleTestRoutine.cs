using System.Collections;
using System.Linq;
using TestLE.Scripting;
using TestLE.Utilities;
using UnityEngine;

/// <summary>
/// A simple test routine that demonstrates the basic workflow:
/// 1. Check for enemies and fight them
/// 2. Loot nearby items
/// 3. Move around if nothing else to do
/// 
/// This script shows how to combine multiple behaviors in a single routine.
/// </summary>
public class SimpleTestRoutine : ScriptBase, IUpdatableScript, IGuiScript
{
    public override string Name => "Simple Test Routine";
    
    // Configuration
    private const float COMBAT_RANGE = 12f;
    private const float LOOT_RANGE = 8f;
    private const float MOVE_RANGE = 5f;
    
    // State tracking
    private RoutineState _currentState = RoutineState.Idle;
    private float _lastStateChange = 0f;
    private Vector3 _lastPosition;
    private float _stuckTimer = 0f;
    private const float STUCK_THRESHOLD = 3f; // Consider stuck if not moving for 3 seconds
    
    private enum RoutineState
    {
        Idle,
        Combat,
        Looting,
        Moving,
        Stuck
    }
    
    public override bool CanExecute()
    {
        // Always execute if player exists - this routine handles all basic behaviors
        return Player != null;
    }
    
    public override IEnumerator Execute()
    {
        Log($"Executing routine in state: {_currentState}");
        
        // Determine what we should do based on current situation
        var newState = DetermineNextState();
        
        if (newState != _currentState)
        {
            Log($"State change: {_currentState} -> {newState}");
            _currentState = newState;
            _lastStateChange = Time.time;
        }
        
        // Execute behavior based on current state
        switch (_currentState)
        {
            case RoutineState.Combat:
                yield return HandleCombat();
                break;
                
            case RoutineState.Looting:
                yield return HandleLooting();
                break;
                
            case RoutineState.Moving:
                yield return HandleMovement();
                break;
                
            case RoutineState.Stuck:
                yield return HandleStuck();
                break;
                
            case RoutineState.Idle:
            default:
                yield return HandleIdle();
                break;
        }
        
        // Update position tracking for stuck detection
        UpdatePositionTracking();
    }
    
    private RoutineState DetermineNextState()
    {
        // Check for stuck condition first
        if (IsPlayerStuck())
        {
            return RoutineState.Stuck;
        }
        
        // Priority 1: Combat if enemies nearby
        var nearbyEnemies = GetNearbyEnemies();
        if (nearbyEnemies.Any())
        {
            return RoutineState.Combat;
        }
        
        // Priority 2: Loot if items nearby
        var nearbyItems = GetNearbyGroundItems();
        if (nearbyItems.Any())
        {
            return RoutineState.Looting;
        }
        
        // Priority 3: Move around if we've been idle too long
        if (_currentState == RoutineState.Idle && Time.time - _lastStateChange > 5f)
        {
            return RoutineState.Moving;
        }
        
        return RoutineState.Idle;
    }
    
    private IEnumerator HandleCombat()
    {
        var target = GetNearestEnemy();
        if (target == null)
        {
            Log("No combat target found");
            yield break;
        }
        
        Log($"Engaging enemy: {target.Data.name}");
        
        // Move to combat range if needed
        var distance = Vector3.Distance(Player.transform.position, target.Data.transform.position);
        if (distance > 5f)
        {
            yield return PlayerHelpers.MoveToForce(target.Data.transform.position);
            yield return Wait(0.5f);
        }
        
        // Simple attack simulation (replace with actual combat logic)
        Log("Attacking enemy");
        yield return Wait(1f); // Simulate attack duration
        
        // Check if we should use a potion
        var healthPercent = Player.playerHealth.currentHealth / Player.playerHealth.maxHealth;
        if (healthPercent < 0.5f)
        {
            try
            {
                PlayerHelpers.UsePotion();
                Log("Used health potion");
            }
            catch (System.Exception ex)
            {
                LogError($"Failed to use potion: {ex.Message}");
            }
        }
    }
    
    private IEnumerator HandleLooting()
    {
        var items = GetNearbyGroundItems().Take(3); // Loot up to 3 items
        
        foreach (var item in items)
        {
            if (item?.gameObject == null) continue;
            
            Log($"Looting item: {item.name}");
            
            // Move to item
            yield return PlayerHelpers.MoveToForce(item.transform.position);
            yield return Wait(0.3f);
            
            // Loot item
            try
            {
                item.ObjectClick(Player.gameObject, true);
                yield return Wait(0.2f);
            }
            catch (System.Exception ex)
            {
                LogError($"Failed to loot {item.name}: {ex.Message}");
            }
        }
    }
    
    private IEnumerator HandleMovement()
    {
        // Move to a random nearby position
        var currentPos = Player.transform.position;
        var randomDirection = new Vector3(
            Random.Range(-1f, 1f),
            0,
            Random.Range(-1f, 1f)
        ).normalized;
        
        var targetPos = currentPos + randomDirection * Random.Range(5f, 10f);
        
        Log($"Moving to random position: {targetPos}");
        yield return PlayerHelpers.MoveToForce(targetPos);
        yield return Wait(1f);
    }
    
    private IEnumerator HandleStuck()
    {
        Log("Player appears to be stuck, attempting to unstuck");
        
        // Try moving in different directions
        var directions = new[]
        {
            Vector3.forward, Vector3.back, Vector3.left, Vector3.right,
            new Vector3(1, 0, 1).normalized, new Vector3(-1, 0, 1).normalized,
            new Vector3(1, 0, -1).normalized, new Vector3(-1, 0, -1).normalized
        };
        
        foreach (var direction in directions)
        {
            var targetPos = Player.transform.position + direction * 3f;
            yield return PlayerHelpers.MoveToForce(targetPos);
            yield return Wait(0.5f);
            
            // Check if we moved
            if (Vector3.Distance(_lastPosition, Player.transform.position) > 1f)
            {
                Log("Successfully unstuck");
                _stuckTimer = 0f;
                break;
            }
        }
    }
    
    private IEnumerator HandleIdle()
    {
        // Just wait a bit when idle
        yield return Wait(0.5f);
    }
    
    public void OnUpdate()
    {
        // Handle manual controls
        if (GetKeyDown(KeyCode.T))
        {
            Log($"Current state: {_currentState}");
            Log($"Nearby enemies: {GetNearbyEnemies().Count()}");
            Log($"Nearby items: {GetNearbyGroundItems().Count()}");
        }
        
        if (GetKeyDown(KeyCode.R))
        {
            Log("Forcing state reset");
            _currentState = RoutineState.Idle;
            _lastStateChange = Time.time;
        }
    }
    
    public void OnGUI()
    {
        // Display routine status
        var rect = new Rect(10, Screen.height - 120, 200, 110);
        GUI.Box(rect, "Simple Test Routine");
        
        GUI.Label(new Rect(rect.x + 5, rect.y + 20, 190, 20), $"State: {_currentState}");
        GUI.Label(new Rect(rect.x + 5, rect.y + 40, 190, 20), $"Enemies: {GetNearbyEnemies().Count()}");
        GUI.Label(new Rect(rect.x + 5, rect.y + 60, 190, 20), $"Items: {GetNearbyGroundItems().Count()}");
        GUI.Label(new Rect(rect.x + 5, rect.y + 80, 190, 20), "T=Info, R=Reset");
    }
    
    private System.Collections.Generic.IEnumerable<Enemy> GetNearbyEnemies()
    {
        if (Player == null) return System.Linq.Enumerable.Empty<Enemy>();
        
        var playerPos = Player.transform.position;
        return Enemies.Where(enemy => 
            enemy?.Data != null && 
            enemy.Data.gameObject.activeInHierarchy &&
            Vector3.Distance(playerPos, enemy.Data.transform.position) <= COMBAT_RANGE);
    }
    
    private System.Collections.Generic.IEnumerable<GroundItem> GetNearbyGroundItems()
    {
        if (Player == null) return System.Linq.Enumerable.Empty<GroundItem>();
        
        var playerPos = Player.transform.position;
        return GroundItems.Where(item => 
            item?.gameObject != null && 
            Vector3.Distance(playerPos, item.transform.position) <= LOOT_RANGE);
    }
    
    private void UpdatePositionTracking()
    {
        if (Player == null) return;
        
        var currentPos = Player.transform.position;
        var distance = Vector3.Distance(_lastPosition, currentPos);
        
        if (distance < 0.5f) // Not moving much
        {
            _stuckTimer += Time.deltaTime;
        }
        else
        {
            _stuckTimer = 0f;
            _lastPosition = currentPos;
        }
    }
    
    private bool IsPlayerStuck()
    {
        return _stuckTimer > STUCK_THRESHOLD;
    }
    
    public override void OnLoad()
    {
        base.OnLoad();
        _currentState = RoutineState.Idle;
        _lastStateChange = Time.time;
        _stuckTimer = 0f;
        if (Player != null)
        {
            _lastPosition = Player.transform.position;
        }
        Log("Simple test routine loaded and ready!");
    }
    
    public override void OnUnload()
    {
        base.OnUnload();
        Log("Simple test routine unloaded");
    }
}
