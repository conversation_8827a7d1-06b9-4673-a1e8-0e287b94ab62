using System.Collections;
using System.Linq;
using TestLE.Scripting;
using TestLE.Utilities;
using UnityEngine;

/// <summary>
/// Example script routine that demonstrates combat functionality.
/// This script will engage nearby enemies and use basic combat tactics.
/// </summary>
public class ExampleCombatRoutine : ScriptBase, IUpdatableScript, IGuiScript
{
    public override string Name => "Example Combat Routine";
    
    private float _lastAttackTime = 0f;
    private const float ATTACK_COOLDOWN = 1f;
    private const float COMBAT_RANGE = 15f;
    private const float FLEE_HEALTH_THRESHOLD = 0.3f; // Flee when health below 30%
    
    // Combat state tracking
    private Enemy? _currentTarget = null;
    private bool _isFleeing = false;
    
    public override bool CanExecute()
    {
        if (Player == null) return false;
        
        // Check if we should flee due to low health
        var healthPercent = Player.playerHealth.currentHealth / Player.playerHealth.maxHealth;
        if (healthPercent <= FLEE_HEALTH_THRESHOLD)
        {
            _isFleeing = true;
            return true; // Execute to handle fleeing
        }
        
        _isFleeing = false;
        
        // Check for nearby enemies
        var nearbyEnemies = GetNearbyEnemies();
        return nearbyEnemies.Any();
    }
    
    public override IEnumerator Execute()
    {
        if (_isFleeing)
        {
            yield return HandleFleeing();
            yield break;
        }
        
        Log("Starting combat routine");
        
        // Find and engage the nearest enemy
        var target = GetNearestEnemy();
        if (target == null)
        {
            Log("No valid target found");
            yield break;
        }
        
        _currentTarget = target;
        Log($"Engaging target: {target.Data.name}");
        
        // Move to combat range
        var targetPos = target.Data.transform.position;
        var playerPos = Player.transform.position;
        var distance = Vector3.Distance(playerPos, targetPos);
        
        if (distance > 5f) // Get closer if too far
        {
            Log("Moving to combat range");
            yield return PlayerHelpers.MoveToForce(targetPos);
            yield return Wait(0.5f);
        }
        
        // Combat loop
        int attackCount = 0;
        while (_currentTarget != null && 
               _currentTarget.Data != null && 
               _currentTarget.Data.gameObject.activeInHierarchy &&
               attackCount < 10) // Prevent infinite loops
        {
            // Check if we should flee
            var healthPercent = Player.playerHealth.currentHealth / Player.playerHealth.maxHealth;
            if (healthPercent <= FLEE_HEALTH_THRESHOLD)
            {
                Log("Health low, breaking combat to flee");
                break;
            }
            
            // Attack if cooldown is ready
            if (Time.time - _lastAttackTime > ATTACK_COOLDOWN)
            {
                yield return PerformAttack();
                attackCount++;
            }
            
            yield return Wait(0.1f); // Small delay between combat checks
        }
        
        _currentTarget = null;
        Log("Combat routine completed");
    }
    
    private IEnumerator HandleFleeing()
    {
        Log("Health critical! Attempting to flee and use potion");
        
        // Try to use a health potion
        try
        {
            PlayerHelpers.UsePotion();
            Log("Used health potion");
        }
        catch (System.Exception ex)
        {
            LogError($"Failed to use potion: {ex.Message}");
        }
        
        // Move away from enemies
        var nearestEnemy = GetNearestEnemy();
        if (nearestEnemy != null)
        {
            var enemyPos = nearestEnemy.Data.transform.position;
            var playerPos = Player.transform.position;
            var fleeDirection = (playerPos - enemyPos).normalized;
            var fleeTarget = playerPos + fleeDirection * 10f; // Flee 10 units away
            
            Log("Fleeing from enemies");
            yield return PlayerHelpers.MoveToForce(fleeTarget);
        }
        
        yield return Wait(2f); // Wait for health to recover
    }
    
    private IEnumerator PerformAttack()
    {
        if (_currentTarget?.Data == null) yield break;
        
        try
        {
            // Face the target
            var targetPos = _currentTarget.Data.transform.position;
            var direction = (targetPos - Player.transform.position).normalized;
            Player.transform.rotation = Quaternion.LookRotation(direction);
            
            // Perform attack (this is game-specific - adjust as needed)
            // For now, we'll simulate an attack
            Log($"Attacking {_currentTarget.Data.name}");
            
            // You would replace this with actual attack logic
            // For example: Player.Attack() or similar
            
            _lastAttackTime = Time.time;
            yield return Wait(0.2f);
        }
        catch (System.Exception ex)
        {
            LogError($"Attack failed: {ex.Message}");
        }
    }
    
    public void OnUpdate()
    {
        // Update combat state and handle input
        if (GetKeyDown(KeyCode.C))
        {
            var enemies = GetNearbyEnemies().ToList();
            Log($"Found {enemies.Count} nearby enemies");
        }
        
        // Clear dead target
        if (_currentTarget?.Data == null || !_currentTarget.Data.gameObject.activeInHierarchy)
        {
            _currentTarget = null;
        }
    }
    
    public void OnGUI()
    {
        // Display combat information
        if (Player == null) return;
        
        var rect = new Rect(Screen.width - 200, 10, 190, 100);
        GUI.Box(rect, "Combat Info");
        
        var healthPercent = Player.playerHealth.currentHealth / Player.playerHealth.maxHealth;
        GUI.Label(new Rect(rect.x + 5, rect.y + 20, 180, 20), $"Health: {healthPercent:P0}");
        
        var nearbyEnemies = GetNearbyEnemies().Count();
        GUI.Label(new Rect(rect.x + 5, rect.y + 40, 180, 20), $"Enemies: {nearbyEnemies}");
        
        var targetName = _currentTarget?.Data?.name ?? "None";
        GUI.Label(new Rect(rect.x + 5, rect.y + 60, 180, 20), $"Target: {targetName}");
        
        if (_isFleeing)
        {
            GUI.Label(new Rect(rect.x + 5, rect.y + 80, 180, 20), "Status: FLEEING");
        }
    }
    
    private System.Collections.Generic.IEnumerable<Enemy> GetNearbyEnemies()
    {
        if (Player == null) return System.Linq.Enumerable.Empty<Enemy>();
        
        var playerPos = Player.transform.position;
        return Enemies.Where(enemy => 
            enemy?.Data != null && 
            enemy.Data.gameObject.activeInHierarchy &&
            Vector3.Distance(playerPos, enemy.Data.transform.position) <= COMBAT_RANGE);
    }
    
    public override void OnLoad()
    {
        base.OnLoad();
        _currentTarget = null;
        _isFleeing = false;
        Log("Combat routine script loaded and ready!");
    }
    
    public override void OnUnload()
    {
        base.OnUnload();
        _currentTarget = null;
        Log("Combat routine script unloaded");
    }
}
